let
    Source = Folder.Files"C:\WorkFileDocuments\DCOS By WK for TAM"),
    #"Sorted Rows" = Table.Sort(Source, {{"Date modified", Order.Descending}}),
    #"Kept First Rows" = Table.FirstN(#"Sorted Rows", 1),
    #"Removed Other Columns" = Table.SelectColumns(#"Kept First Rows", {"Content"}),
    #"Added Custom" = Table.AddColumn(#"Removed Other Columns", "Custom", each Excel.Workbook([Content])),
    #"Expanded Custom" = Table.ExpandTableColumn(#"Added Custom", "Custom", {"Data"}, {"Data"}),
    #"Removed Other Columns1" = Table.SelectColumns(#"Expanded Custom", {"Data"}),
    #"Expanded Data" = Table.ExpandTableColumn(#"Removed Other Columns1", "Data", 
        {"Column1", "Column2", "Column3", "Column4", "Column5", "Column6", "Column7", "Column8", "Column9", "Column10", "Column11", "Column12", "Column13", "Column14", "Column15", "Column16", "Column17", "Column18", "Column19", "Column20", "Column21", "Column22", "Column23", "Column24", "Column25", "Column26", "Column27", "Column28", "Column29", "Column30", "Column31", "Column32", "Column33", "Column34", "Column35", "Column36", "Column37", "Column38", "Column39", "Column40", "Column41", "Column42", "Column43", "Column44", "Column45", "Column46", "Column47", "Column48", "Column49", "Column50", "Column51", "Column52", "Column53", "Column54", "Column55", "Column56", "Column57", "Column58", "Column59"}),
    #"Promoted Headers" = Table.PromoteHeaders(#"Expanded Data", [PromoteAllScalars=true]),
    #"Filtered Rows1" = Table.SelectRows(#"Promoted Headers", each not Text.Contains([Supplier], ";")),
    StaticColumns = {"Critical Flag", "Part", "Part Description", "DISCP Site", "Supplier", "Buyer", "GSM", "Data Measure"},
    #"Changed Type" = Table.TransformColumnTypes(#"Filtered Rows1",
        {{"Critical Flag", type text}, {"Part", type text}, {"Part Description", type text}, {"DISCP Site", type text}, {"Supplier", type any}, {"Buyer", type text}, {"GSM", type text}, {"Data Measure", type text}}
        & List.Transform(List.Skip(Table.ColumnNames(#"Filtered Rows1"), 8), each {_, type number})),
    #"Filtered Rows" = Table.SelectRows(#"Changed Type", each ([Data Measure] = "Released Forecast")),
    DateColumns = List.Skip(Table.ColumnNames(#"Filtered Rows"), 8),
    #"Added TotalSum" = Table.AddColumn(#"Filtered Rows", "TotalSum", each List.Sum(Record.ToList(Record.SelectFields(_, DateColumns)))),
    DeleteDateColumns = Table.RemoveColumns(#"Added TotalSum", DateColumns),
    // 分组，计算组的总量
    #"Grouped Rows" = Table.Group(DeleteDateColumns, {"DISCP Site", "Part"}, {{"TotalSumGroup", each List.Sum([TotalSum]), type nullable number}}),
    // 合并分组结果回原数据表
    #"Merged Queries" = Table.NestedJoin(DeleteDateColumns, {"DISCP Site", "Part"}, #"Grouped Rows", {"DISCP Site", "Part"}, "GroupedData", JoinKind.LeftOuter),
    #"Expanded GroupedData" = Table.ExpandTableColumn(#"Merged Queries", "GroupedData", {"TotalSumGroup"}),
    #"Filtered Rows2" = Table.SelectRows(#"Expanded GroupedData", each ([TotalSum] <> 0)),
    // 计算比例
    #"Added CustomSite" = Table.AddColumn(#"Filtered Rows2", "Proportion", each if [TotalSumGroup] = null then null else [TotalSum] / [TotalSumGroup], type number),
    #"Duplicated Column" = Table.DuplicateColumn(#"Added CustomSite", "DISCP Site", "Customer Site"),
    #"Changed Type1" = Table.TransformColumnTypes(#"Duplicated Column",{{"Proportion", Percentage.Type}}),
    #"Replaced Value" = Table.ReplaceValue(#"Changed Type1", "T2_COMCHE", "COMPALChina", Replacer.ReplaceText, {"DISCP Site"}),
    #"Replaced Value1" = Table.ReplaceValue(#"Replaced Value", "T2_COMHAN", "COMPALVietnam", Replacer.ReplaceText, {"DISCP Site"}),
    #"Replaced Value2" = Table.ReplaceValue(#"Replaced Value1", "T2_COMKUN", "COMPALChina", Replacer.ReplaceText, {"DISCP Site"}),
    #"Replaced Value3" = Table.ReplaceValue(#"Replaced Value2", "T2_WISCHE", "WISTRONChina", Replacer.ReplaceText, {"DISCP Site"}),
    #"Replaced Value4" = Table.ReplaceValue(#"Replaced Value3", "T2_WISHNM", "WISTRONVietnam", Replacer.ReplaceText, {"DISCP Site"}),
    #"Inserted Merged Column" = Table.AddColumn(#"Replaced Value4", "MergedColumn", each Text.Combine({[DISCP Site], [Supplier], [#"Part"]}, ""), type text),
    #"Merged Queries1" = Table.NestedJoin(#"Inserted Merged Column", {"MergedColumn"}, TAM, {"Merged"}, "TAM", JoinKind.LeftOuter),

    // 动态获取TAM列名，增加错误处理
    TAMColumns = let
        TAMColumnNames = try Table.ColumnNames(TAM) otherwise {},    // 安全获取列名
        FilteredTAMColumns = List.Select(TAMColumnNames, each Text.StartsWith(_, "Q") and Text.EndsWith(_, "TAM"))    // 过滤符合条件的列名
    in
        if List.IsEmpty(FilteredTAMColumns) then {"Q3 TAM"} else FilteredTAMColumns,    // 如果没有找到TAM列，使用默认值

    // 定义静态列名列表，提高可维护性
    BaseColumns = {"Part", "Supplier", "Buyer", "GSM", "TotalSum", "TotalSumGroup", "Proportion", "Customer Site"},
    ReorderedBaseColumns = {"Customer Site", "Part", "Supplier", "Buyer", "GSM", "TotalSum", "TotalSumGroup", "Proportion"},

    // 获取主要TAM列名（用于计算和比较）
    PrimaryTAMColumn = List.First(TAMColumns),

    #"Expanded TAM" = Table.ExpandTableColumn(#"Merged Queries1", "TAM", TAMColumns, TAMColumns),
    #"Rounded Off" = Table.TransformColumns(#"Expanded TAM", {{"Proportion", each Number.Round(_, 2), Percentage.Type}}),
    #"Removed Other Columns2" = Table.SelectColumns(#"Rounded Off", BaseColumns & TAMColumns),
    #"Reordered Columns1" = Table.ReorderColumns(#"Removed Other Columns2", ReorderedBaseColumns & TAMColumns),
    #"Rounded Off1" = Table.TransformColumns(#"Reordered Columns1", {{PrimaryTAMColumn, each Number.Round(_, 2), Percentage.Type}}),
    #"Added Custom1" = Table.AddColumn(#"Rounded Off1", "Gap>2%", each
        if [Proportion] = null or Record.Field(_, PrimaryTAMColumn) = null then
            null
        else if Number.Abs([Proportion] - Record.Field(_, PrimaryTAMColumn)) > 0.02 then
            true
        else
            false),
    #"Sorted Rows1" = Table.Sort(#"Added Custom1",{{"Buyer", Order.Ascending}, {"Gap>2%", Order.Descending}, {"Part", Order.Ascending}, {"Customer Site", Order.Ascending}})
in
    #"Sorted Rows1"
